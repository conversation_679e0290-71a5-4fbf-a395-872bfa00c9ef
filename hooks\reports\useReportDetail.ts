"use client";

import { useState } from "react";

import { ApiReport } from "@/types/report";
import { ReportsService } from "@/services/reports";

export function useReportDetail() {
  const [reportDetail, setReportDetail] = useState<ApiReport | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchReportDetail = async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      const data = await ReportsService.getReport(parseInt(id));

      setReportDetail(data);

      return data;
    } catch (err: any) {
      // If API fails, create a fallback generic report
      const fallbackReport: ApiReport = {
        id: parseInt(id),
        name: "Reporte Genérico",
        description: "Descripción del reporte",
        document_name: "reporte_generico",
        use_english: false,
        observations: true,
        active: true,
        created_at: new Date().toISOString(),
        filters: {},
        fields: [],
      };

      console.warn("Failed to fetch report, using fallback data", err);
      setReportDetail(fallbackReport);

      return fallbackReport;
    } finally {
      setLoading(false);
    }
  };

  const clearReportDetail = () => {
    setReportDetail(null);
    setError(null);
  };

  return {
    reportDetail,
    loading,
    error,
    fetchReportDetail,
    clearReportDetail,
  };
}
